const express = require('express');
const router = express.Router();
const path = require('path');

// Import middleware
const { 
  requireAdminAuth,
  requireStudentAuth,
  optionalAuth,
  addSessionInfo 
} = require('../middleware/auth');
const { longCacheMiddleware, noCacheMiddleware } = require('../middleware/cache');

// Helper function to serve HTML files
const serveHTML = (filename) => {
  return (req, res) => {
    res.sendFile(path.join(__dirname, '../../views', filename));
  };
};

// Public pages
router.get('/',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(3600), // 1 hour cache
  serveHTML('landing.html')
);

router.get('/login',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('login.html')
);

router.get('/register',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('student-register.html')  // Fixed: register.html -> student-register.html
);

router.get('/student/login',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('student-login.html')
);

router.get('/student/register',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('student-register.html')
);

router.get('/lessons',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('index.html')
);

router.get('/lesson/:id',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('lesson.html')
);

// Additional routes referenced in landing page
router.get('/lythuyet',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('index.html') // Redirect to lessons page for now
);

router.get('/multiplechoice',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('index.html') // Redirect to lessons page for now
);

router.get('/leaderboard',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(300), // 5 minutes cache
  serveHTML('leaderboard.html')
);

router.get('/gallery',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('gallery.html')
);

router.get('/history',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(1800), // 30 minutes cache
  serveHTML('history.html')
);

router.get('/quizgame',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('quizgame.html')
);

router.get('/profile',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('profile.html')
);

// Student-only pages
router.get('/student/dashboard',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('index.html')  // Fixed: student-dashboard.html -> index.html (main lessons page for students)
);

router.get('/student/profile',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('profile.html')  // Fixed: student-profile.html -> profile.html
);

router.get('/student/results',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('result.html')  // Fixed: student-results.html -> result.html
);

router.get('/student/rating',
  requireStudentAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('leaderboard.html')  // Fixed: student-rating.html -> leaderboard.html
);

// Admin-only pages
router.get('/admin',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-list.html')  // Fixed: admin.html -> admin-list.html
);

router.get('/admin/login',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-login.html')
);

router.get('/admin/lessons',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-list.html')  // Fixed: admin-lessons.html -> admin-list.html (lessons are managed in main admin page)
);

router.get('/admin/lessons/new',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-edit.html')  // Fixed: admin-lesson-new.html -> admin-edit.html
);

router.get('/admin/lessons/:id/edit',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-edit.html')  // Fixed: admin-lesson-edit.html -> admin-edit.html
);

// Admin configure page (for lesson configuration after editing)
router.get('/admin/configure',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-configure.html')
);

router.get('/admin/configure/:id',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-configure.html')
);

router.get('/admin/students',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-students.html')
);

router.get('/admin/results',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-list.html')  // Fixed: admin-results.html -> admin-list.html (results managed in main admin page)
);

router.get('/admin/ratings',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-list.html')  // Fixed: admin-ratings.html -> admin-list.html (ratings managed in main admin page)
);

router.get('/admin/uploads',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-list.html')  // Fixed: admin-uploads.html -> admin-list.html (uploads managed in main admin page)
);

router.get('/admin/statistics',
  requireAdminAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('admin-list.html')  // Fixed: admin-statistics.html -> admin-list.html (statistics managed in main admin page)
);

// Result viewing pages
router.get('/result/:id',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(86400), // 24 hours cache
  serveHTML('result.html')
);

// Error pages
router.get('/404',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(3600), // 1 hour cache
  serveHTML('404.html')
);

router.get('/500',
  optionalAuth,
  addSessionInfo,
  noCacheMiddleware,
  serveHTML('404.html')  // Fixed: 500.html -> 404.html (use 404 page for now)
);

// API documentation page
router.get('/docs',
  optionalAuth,
  addSessionInfo,
  longCacheMiddleware(3600), // 1 hour cache
  serveHTML('404.html')  // Fixed: api-docs.html -> 404.html (use 404 page for now)
);

// Health check page
router.get('/health',
  longCacheMiddleware(60), // 1 minute cache
  (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage()
    });
  }
);

module.exports = router;
