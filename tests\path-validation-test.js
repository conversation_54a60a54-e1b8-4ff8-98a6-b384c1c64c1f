/**
 * Path Validation Test
 * Tests all route definitions to ensure they point to existing HTML files
 */

const fs = require('fs');
const path = require('path');

// Define all routes and their expected HTML files
const routeToFileMapping = {
  // Public routes
  '/': 'landing.html',
  '/login': 'login.html',
  '/register': 'student-register.html',
  '/student/login': 'student-login.html',
  '/student/register': 'student-register.html',
  '/lessons': 'index.html',
  '/lesson/:id': 'lesson.html',
  '/lythuyet': 'index.html',
  '/multiplechoice': 'index.html',
  '/leaderboard': 'leaderboard.html',
  '/gallery': 'gallery.html',
  '/history': 'history.html',
  '/quizgame': 'quizgame.html',
  '/profile': 'profile.html',
  
  // Student routes
  '/student/dashboard': 'index.html',
  '/student/profile': 'profile.html',
  '/student/results': 'result.html',
  '/student/rating': 'leaderboard.html',
  
  // Admin routes
  '/admin': 'admin-list.html',
  '/admin/login': 'admin-login.html',
  '/admin/lessons': 'admin-list.html',
  '/admin/lessons/new': 'admin-edit.html',
  '/admin/lessons/:id/edit': 'admin-edit.html',
  '/admin/configure': 'admin-configure.html',
  '/admin/configure/:id': 'admin-configure.html',
  '/admin/students': 'admin-students.html',
  '/admin/results': 'admin-list.html',
  '/admin/ratings': 'admin-list.html',
  '/admin/uploads': 'admin-list.html',
  '/admin/statistics': 'admin-list.html',
  
  // Other routes
  '/result/:id': 'result.html',
  '/404': '404.html',
  '/500': '404.html',
  '/docs': '404.html'
};

function validatePaths() {
  console.log('🔍 Path Validation Test');
  console.log('='.repeat(50));
  
  const viewsDir = path.join(process.cwd(), 'views');
  console.log(`Views directory: ${viewsDir}`);
  console.log('');
  
  let totalRoutes = 0;
  let validRoutes = 0;
  let invalidRoutes = [];
  
  // Check each route
  for (const [route, htmlFile] of Object.entries(routeToFileMapping)) {
    totalRoutes++;
    const filePath = path.join(viewsDir, htmlFile);
    const exists = fs.existsSync(filePath);
    
    if (exists) {
      validRoutes++;
      console.log(`✅ ${route} → ${htmlFile}`);
    } else {
      invalidRoutes.push({ route, htmlFile, filePath });
      console.log(`❌ ${route} → ${htmlFile} (FILE NOT FOUND)`);
    }
  }
  
  console.log('');
  console.log('📊 Summary');
  console.log('-'.repeat(30));
  console.log(`Total routes tested: ${totalRoutes}`);
  console.log(`Valid routes: ${validRoutes}`);
  console.log(`Invalid routes: ${invalidRoutes.length}`);
  
  if (invalidRoutes.length > 0) {
    console.log('');
    console.log('❌ Missing Files:');
    invalidRoutes.forEach(({ route, htmlFile, filePath }) => {
      console.log(`   Route: ${route}`);
      console.log(`   Expected file: ${htmlFile}`);
      console.log(`   Full path: ${filePath}`);
      console.log('');
    });
    
    return false;
  } else {
    console.log('');
    console.log('🎉 All routes point to existing files!');
    return true;
  }
}

function listActualFiles() {
  console.log('');
  console.log('📁 Actual files in views directory:');
  console.log('-'.repeat(40));
  
  const viewsDir = path.join(process.cwd(), 'views');
  try {
    const files = fs.readdirSync(viewsDir)
      .filter(file => file.endsWith('.html'))
      .sort();
    
    files.forEach(file => {
      console.log(`   ${file}`);
    });
    
    console.log(`\nTotal HTML files: ${files.length}`);
  } catch (error) {
    console.error(`Error reading views directory: ${error.message}`);
  }
}

function checkForUnusedFiles() {
  console.log('');
  console.log('🔍 Checking for unused HTML files:');
  console.log('-'.repeat(40));
  
  const viewsDir = path.join(process.cwd(), 'views');
  const usedFiles = new Set(Object.values(routeToFileMapping));
  
  try {
    const allFiles = fs.readdirSync(viewsDir)
      .filter(file => file.endsWith('.html'));
    
    const unusedFiles = allFiles.filter(file => !usedFiles.has(file));
    
    if (unusedFiles.length > 0) {
      console.log('⚠️  Unused files (not referenced in routes):');
      unusedFiles.forEach(file => {
        console.log(`   ${file}`);
      });
    } else {
      console.log('✅ All HTML files are referenced in routes');
    }
  } catch (error) {
    console.error(`Error checking unused files: ${error.message}`);
  }
}

// Run the validation
if (require.main === module) {
  const isValid = validatePaths();
  listActualFiles();
  checkForUnusedFiles();
  
  console.log('');
  console.log('='.repeat(50));
  console.log(isValid ? '✅ PATH VALIDATION PASSED' : '❌ PATH VALIDATION FAILED');
  
  process.exit(isValid ? 0 : 1);
}

module.exports = { validatePaths, routeToFileMapping };
