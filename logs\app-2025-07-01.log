[2025-07-01T17:27:31.551Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:96:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:27:50.304Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:96:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:28:38.184Z] [ERROR] FATAL: Uncaught Exception | {"error":"app.use() requires a middleware function","stack":"TypeError: app.use() requires a middleware function\n    at Function.use (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:217:11)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:109:5)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:29:14.963Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:29:14.963Z"}
[2025-07-01T17:29:27.923Z] [INFO] GET / - 500 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"15ms","timestamp":"2025-07-01T17:29:27.923Z"}
[2025-07-01T17:29:28.391Z] [INFO] SIGINT received, shutting down gracefully
[2025-07-01T17:29:28.394Z] [INFO] Process terminated
[2025-07-01T17:29:52.376Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:29:52.375Z"}
[2025-07-01T17:30:38.785Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:30:38.784Z"}
[2025-07-01T17:30:49.809Z] [INFO] GET / - 200 - 15ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:30:49.809Z"}
[2025-07-01T17:30:49.834Z] [INFO] GET /js/network-animation.js - 200 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:30:49.834Z"}
[2025-07-01T17:30:49.838Z] [INFO] GET /css/style.css - 200 - 8ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"8ms","timestamp":"2025-07-01T17:30:49.838Z"}
[2025-07-01T17:30:49.843Z] [INFO] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:49.843Z"}
[2025-07-01T17:30:50.084Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:30:50.088Z] [INFO] GET /api/check-student-auth - 404 - 7ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:30:50.088Z"}
[2025-07-01T17:30:50.100Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:30:50.103Z] [INFO] GET /student/login - 404 - 4ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:50.103Z"}
[2025-07-01T17:30:51.328Z] [INFO] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:30:51.328Z"}
[2025-07-01T17:30:51.334Z] [WARN] 404 Not Found | {"url":"/api/auth/status","method":"GET","ip":"::1"}
[2025-07-01T17:30:51.337Z] [INFO] GET /api/auth/status - 404 - 4ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:30:51.337Z"}
[2025-07-01T17:30:51.341Z] [WARN] 404 Not Found | {"url":"/api/auth/status","method":"GET","ip":"::1"}
[2025-07-01T17:30:51.343Z] [INFO] GET /api/auth/status - 404 - 3ms | {"method":"GET","url":"/api/auth/status","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:51.343Z"}
[2025-07-01T17:30:52.141Z] [INFO] GET / - 200 - 795ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"795ms","timestamp":"2025-07-01T17:30:52.141Z"}
[2025-07-01T17:30:52.144Z] [WARN] 404 Not Found | {"url":"/api/ratings","method":"GET","ip":"::1"}
[2025-07-01T17:30:52.146Z] [INFO] GET /api/ratings - 404 - 2ms | {"method":"GET","url":"/api/ratings","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.146Z"}
[2025-07-01T17:30:52.149Z] [WARN] 404 Not Found | {"url":"/favicon.ico","method":"GET","ip":"::1"}
[2025-07-01T17:30:52.151Z] [INFO] GET /favicon.ico - 404 - 3ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:30:52.151Z"}
[2025-07-01T17:30:52.154Z] [WARN] 404 Not Found | {"url":"/api/nonexistent","method":"GET","ip":"::1"}
[2025-07-01T17:30:52.155Z] [INFO] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:30:52.155Z"}
[2025-07-01T17:34:12.538Z] [INFO] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:34:12.538Z"}
[2025-07-01T17:34:12.544Z] [INFO] GET /check - 200 - 1ms | {"method":"GET","url":"/check","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.544Z"}
[2025-07-01T17:34:12.548Z] [INFO] GET /session - 200 - 1ms | {"method":"GET","url":"/session","ip":"::1","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:34:12.548Z"}
[2025-07-01T17:34:13.122Z] [INFO] GET / - 200 - 571ms | {"method":"GET","url":"/","ip":"::1","statusCode":200,"responseTime":"571ms","timestamp":"2025-07-01T17:34:13.122Z"}
[2025-07-01T17:34:13.203Z] [INFO] GET /leaderboard - 200 - 79ms | {"method":"GET","url":"/leaderboard","ip":"::1","statusCode":200,"responseTime":"79ms","timestamp":"2025-07-01T17:34:13.203Z"}
[2025-07-01T17:34:13.206Z] [WARN] 404 Not Found | {"url":"/favicon.ico","method":"GET","ip":"::1"}
[2025-07-01T17:34:13.208Z] [INFO] GET /favicon.ico - 404 - 2ms | {"method":"GET","url":"/favicon.ico","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.208Z"}
[2025-07-01T17:34:13.211Z] [WARN] 404 Not Found | {"url":"/api/nonexistent","method":"GET","ip":"::1"}
[2025-07-01T17:34:13.212Z] [INFO] GET /api/nonexistent - 404 - 2ms | {"method":"GET","url":"/api/nonexistent","ip":"::1","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:13.212Z"}
[2025-07-01T17:34:37.445Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:34:37.445Z"}
[2025-07-01T17:34:39.618Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:39.623Z] [INFO] GET /student/login - 404 - 11ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"11ms","timestamp":"2025-07-01T17:34:39.623Z"}
[2025-07-01T17:34:44.597Z] [INFO] GET / - 304 - 10ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"10ms","timestamp":"2025-07-01T17:34:44.597Z"}
[2025-07-01T17:34:44.850Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:34:44.856Z] [INFO] GET /api/check-student-auth - 404 - 9ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"9ms","timestamp":"2025-07-01T17:34:44.856Z"}
[2025-07-01T17:34:44.875Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:44.880Z] [INFO] GET /student/login - 404 - 7ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:34:44.880Z"}
[2025-07-01T17:34:53.947Z] [INFO] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:34:53.947Z"}
[2025-07-01T17:34:54.091Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.093Z] [INFO] GET /api/check-student-auth - 404 - 3ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.093Z"}
[2025-07-01T17:34:54.098Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.099Z] [INFO] GET /student/login - 404 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.099Z"}
[2025-07-01T17:34:54.170Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:34:54.170Z"}
[2025-07-01T17:34:54.210Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.213Z] [INFO] GET /api/check-student-auth - 404 - 4ms | {"method":"GET","url":"/api/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:34:54.213Z"}
[2025-07-01T17:34:54.221Z] [WARN] 404 Not Found | {"url":"/student/login","method":"GET","ip":"::1"}
[2025-07-01T17:34:54.223Z] [INFO] GET /student/login - 404 - 3ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:34:54.223Z"}
[2025-07-01T17:35:20.116Z] [INFO] SIGINT received, shutting down gracefully
[2025-07-01T17:35:20.117Z] [INFO] Process terminated
[2025-07-01T17:37:09.326Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:37:09.326Z"}
[2025-07-01T17:37:15.632Z] [INFO] GET / - 304 - 11ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"11ms","timestamp":"2025-07-01T17:37:15.632Z"}
[2025-07-01T17:37:15.796Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:37:15.800Z] [INFO] GET / - 404 - 6ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"6ms","timestamp":"2025-07-01T17:37:15.800Z"}
[2025-07-01T17:37:15.807Z] [INFO] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.807Z"}
[2025-07-01T17:37:15.847Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.847Z"}
[2025-07-01T17:37:15.889Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:37:15.892Z] [INFO] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:37:15.892Z"}
[2025-07-01T17:37:15.906Z] [INFO] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.906Z"}
[2025-07-01T17:37:15.940Z] [INFO] GET /js/device-id.js - 200 - 2ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:15.940Z"}
[2025-07-01T17:37:18.126Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:37:18.126Z"}
[2025-07-01T17:37:18.177Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:37:18.180Z] [INFO] GET / - 404 - 5ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"5ms","timestamp":"2025-07-01T17:37:18.180Z"}
[2025-07-01T17:37:18.198Z] [INFO] GET /student/login - 200 - 4ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:37:18.198Z"}
[2025-07-01T17:37:20.958Z] [WARN] 404 Not Found | {"url":"/api/student/login","method":"POST","ip":"::1"}
[2025-07-01T17:37:20.961Z] [INFO] POST / - 404 - 15ms | {"method":"POST","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"15ms","timestamp":"2025-07-01T17:37:20.961Z"}
[2025-07-01T17:37:23.089Z] [WARN] 404 Not Found | {"url":"/api/student/login","method":"POST","ip":"::1"}
[2025-07-01T17:37:23.092Z] [INFO] POST / - 404 - 4ms | {"method":"POST","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:37:23.092Z"}
[2025-07-01T17:37:30.729Z] [INFO] GET / - 304 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"4ms","timestamp":"2025-07-01T17:37:30.729Z"}
[2025-07-01T17:37:30.789Z] [WARN] 404 Not Found | {"url":"/api/check-student-auth","method":"GET","ip":"::1"}
[2025-07-01T17:37:30.793Z] [INFO] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:37:30.792Z"}
[2025-07-01T17:37:30.805Z] [INFO] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:37:30.805Z"}
[2025-07-01T17:37:32.808Z] [INFO] GET /admin/login - 500 - 3ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"3ms","timestamp":"2025-07-01T17:37:32.808Z"}
[2025-07-01T17:38:23.294Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:38:23.294Z"}
[2025-07-01T17:38:25.825Z] [INFO] GET /admin/login - 500 - 24ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"24ms","timestamp":"2025-07-01T17:38:25.825Z"}
[2025-07-01T17:38:27.957Z] [INFO] GET /student/login - 200 - 6ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T17:38:27.957Z"}
[2025-07-01T17:38:30.614Z] [INFO] POST /api/student/login - 500 - 12ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"12ms","timestamp":"2025-07-01T17:38:30.614Z"}
[2025-07-01T17:38:33.948Z] [INFO] GET / - 304 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:38:33.948Z"}
[2025-07-01T17:38:34.005Z] [INFO] GET /check-student-auth - 200 - 3ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:38:34.005Z"}
[2025-07-01T17:38:34.029Z] [INFO] GET /student/login?redirect=%2F - 200 - 3ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:38:34.029Z"}
[2025-07-01T17:38:36.262Z] [INFO] GET /student/register - 200 - 3ms | {"method":"GET","url":"/student/register","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:38:36.262Z"}
[2025-07-01T17:38:40.123Z] [INFO] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:38:40.123Z"}
[2025-07-01T17:38:41.553Z] [INFO] GET /gallery - 200 - 5ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:38:41.553Z"}
[2025-07-01T17:38:41.582Z] [WARN] 404 Not Found | {"url":"/css/gallery.css","method":"GET","ip":"::1"}
[2025-07-01T17:38:41.588Z] [INFO] GET / - 404 - 8ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"8ms","timestamp":"2025-07-01T17:38:41.588Z"}
[2025-07-01T17:38:41.591Z] [INFO] GET /js/gallery.js - 200 - 5ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:38:41.591Z"}
[2025-07-01T17:38:41.602Z] [INFO] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:38:41.602Z"}
[2025-07-01T17:38:41.658Z] [INFO] GET /student/login?redirect=%2Fgallery - 200 - 38ms | {"method":"GET","url":"/student/login?redirect=%2Fgallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"38ms","timestamp":"2025-07-01T17:38:41.658Z"}
[2025-07-01T17:39:10.385Z] [INFO] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:10.385Z"}
[2025-07-01T17:39:10.417Z] [INFO] GET /css/style.css - 200 - 4ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:39:10.417Z"}
[2025-07-01T17:39:10.428Z] [INFO] GET /js/network-animation.js - 200 - 12ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:39:10.428Z"}
[2025-07-01T17:39:10.434Z] [INFO] GET /js/index.js - 200 - 15ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:39:10.434Z"}
[2025-07-01T17:39:10.836Z] [INFO] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:10.836Z"}
[2025-07-01T17:39:10.845Z] [INFO] GET /student/login?redirect=%2F - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:10.845Z"}
[2025-07-01T17:39:10.864Z] [INFO] GET /js/device-id.js - 200 - 1ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:39:10.864Z"}
[2025-07-01T17:39:12.031Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:39:12.031Z"}
[2025-07-01T17:39:12.094Z] [INFO] GET /check-student-auth - 200 - 4ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:39:12.094Z"}
[2025-07-01T17:39:12.129Z] [INFO] GET /student/login?redirect=%2F - 200 - 1ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:39:12.129Z"}
[2025-07-01T17:39:14.341Z] [INFO] POST /api/student/login - 500 - 4ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"4ms","timestamp":"2025-07-01T17:39:14.341Z"}
[2025-07-01T17:39:16.604Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-01T17:39:16.607Z] [INFO] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:39:16.607Z"}
[2025-07-01T17:39:17.552Z] [INFO] GET /student/login - 200 - 309ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"309ms","timestamp":"2025-07-01T17:39:17.552Z"}
[2025-07-01T17:39:21.014Z] [INFO] GET /admin/login - 200 - 32ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"32ms","timestamp":"2025-07-01T17:39:21.014Z"}
[2025-07-01T17:39:23.349Z] [INFO] GET / - 200 - 33ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"33ms","timestamp":"2025-07-01T17:39:23.349Z"}
[2025-07-01T17:39:23.577Z] [INFO] GET /check-student-auth - 200 - 32ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"32ms","timestamp":"2025-07-01T17:39:23.577Z"}
[2025-07-01T17:39:23.727Z] [INFO] GET /student/login?redirect=%2F - 200 - 29ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"29ms","timestamp":"2025-07-01T17:39:23.727Z"}
[2025-07-01T17:39:35.717Z] [INFO] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:35.717Z"}
[2025-07-01T17:39:40.896Z] [INFO] GET /student/login - 200 - 2ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:40.896Z"}
[2025-07-01T17:39:40.936Z] [INFO] GET /css/style.css - 200 - 2ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:40.936Z"}
[2025-07-01T17:39:40.942Z] [INFO] GET /js/device-id.js - 200 - 2ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:40.942Z"}
[2025-07-01T17:39:42.638Z] [INFO] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:42.638Z"}
[2025-07-01T17:39:42.669Z] [INFO] GET /js/network-animation.js - 200 - 2ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:39:42.669Z"}
[2025-07-01T17:39:42.671Z] [INFO] GET /js/index.js - 200 - 3ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:39:42.671Z"}
[2025-07-01T17:39:42.719Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:39:42.719Z"}
[2025-07-01T17:39:42.746Z] [INFO] GET /student/login?redirect=%2F - 200 - 4ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:39:42.746Z"}
[2025-07-01T17:39:47.725Z] [INFO] POST /api/student/login - 500 - 3ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"3ms","timestamp":"2025-07-01T17:39:47.725Z"}
[2025-07-01T17:40:11.134Z] [INFO] SIGINT received, shutting down gracefully
[2025-07-01T17:40:14.130Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:40:14.130Z"}
[2025-07-01T17:40:15.325Z] [INFO] GET /student/login?redirect=%2F - 200 - 14ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-01T17:40:15.325Z"}
[2025-07-01T17:40:17.446Z] [INFO] POST /api/student/login - 500 - 16ms | {"method":"POST","url":"/api/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"16ms","timestamp":"2025-07-01T17:40:17.446Z"}
[2025-07-01T17:41:34.545Z] [INFO] SIGINT received, shutting down gracefully
[2025-07-01T17:41:34.546Z] [INFO] Process terminated
[2025-07-01T17:41:36.208Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:41:36.208Z"}
[2025-07-01T17:41:37.458Z] [INFO] GET /student/login?redirect=%2F - 200 - 13ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"13ms","timestamp":"2025-07-01T17:41:37.458Z"}
[2025-07-01T17:41:41.128Z] [INFO] POST /student/login - 200 - 1106ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1106ms","timestamp":"2025-07-01T17:41:41.128Z"}
[2025-07-01T17:41:41.713Z] [INFO] GET / - 304 - 64ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"64ms","timestamp":"2025-07-01T17:41:41.713Z"}
[2025-07-01T17:41:41.819Z] [INFO] GET /check-student-auth - 200 - 61ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T17:41:41.819Z"}
[2025-07-01T17:41:41.895Z] [INFO] GET /student/login?redirect=%2F - 200 - 59ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T17:41:41.895Z"}
[2025-07-01T17:41:43.733Z] [INFO] GET /student/login - 200 - 8ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"8ms","timestamp":"2025-07-01T17:41:43.732Z"}
[2025-07-01T17:41:48.857Z] [INFO] POST /student/login - 400 - 66ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"66ms","timestamp":"2025-07-01T17:41:48.857Z"}
[2025-07-01T17:41:50.640Z] [INFO] GET /student/login?redirect=%2F - 200 - 62ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T17:41:50.639Z"}
[2025-07-01T17:41:51.383Z] [INFO] GET / - 304 - 61ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"61ms","timestamp":"2025-07-01T17:41:51.383Z"}
[2025-07-01T17:41:51.623Z] [INFO] GET /check-student-auth - 200 - 197ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"197ms","timestamp":"2025-07-01T17:41:51.623Z"}
[2025-07-01T17:41:51.704Z] [INFO] GET /student/login?redirect=%2F - 200 - 61ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T17:41:51.704Z"}
[2025-07-01T17:41:54.120Z] [INFO] GET /js/gallery.js - 200 - 2ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:41:54.120Z"}
[2025-07-01T17:41:54.125Z] [INFO] GET /gallery - 200 - 60ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"60ms","timestamp":"2025-07-01T17:41:54.125Z"}
[2025-07-01T17:41:54.323Z] [WARN] 404 Not Found | {"url":"/css/gallery.css","method":"GET","ip":"::1"}
[2025-07-01T17:41:54.355Z] [INFO] GET / - 404 - 239ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"239ms","timestamp":"2025-07-01T17:41:54.355Z"}
[2025-07-01T17:41:54.398Z] [INFO] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-01T17:41:54.398Z"}
[2025-07-01T17:41:54.473Z] [INFO] GET /student/login?redirect=%2Fgallery - 200 - 62ms | {"method":"GET","url":"/student/login?redirect=%2Fgallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T17:41:54.473Z"}
[2025-07-01T17:41:55.904Z] [INFO] GET /admin/login - 200 - 60ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"60ms","timestamp":"2025-07-01T17:41:55.904Z"}
[2025-07-01T17:42:00.144Z] [INFO] POST /admin/login - 400 - 62ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"62ms","timestamp":"2025-07-01T17:42:00.144Z"}
[2025-07-01T17:42:02.428Z] [INFO] GET / - 304 - 61ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"61ms","timestamp":"2025-07-01T17:42:02.428Z"}
[2025-07-01T17:42:02.526Z] [INFO] GET /check-student-auth - 200 - 59ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"59ms","timestamp":"2025-07-01T17:42:02.526Z"}
[2025-07-01T17:42:02.601Z] [INFO] GET /student/login?redirect=%2F - 200 - 61ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"61ms","timestamp":"2025-07-01T17:42:02.601Z"}
[2025-07-01T17:42:33.823Z] [INFO] GET / - 304 - 268ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"268ms","timestamp":"2025-07-01T17:42:33.823Z"}
[2025-07-01T17:42:34.124Z] [INFO] GET /check-student-auth - 200 - 71ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T17:42:34.124Z"}
[2025-07-01T17:42:34.203Z] [INFO] GET /student/login?redirect=%2F - 200 - 69ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T17:42:34.203Z"}
[2025-07-01T17:42:36.750Z] [INFO] POST /student/login - 400 - 66ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"66ms","timestamp":"2025-07-01T17:42:36.750Z"}
[2025-07-01T17:42:40.676Z] [INFO] GET /student/login?redirect=%2F - 200 - 67ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:42:40.676Z"}
[2025-07-01T17:42:47.068Z] [INFO] GET / - 304 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"68ms","timestamp":"2025-07-01T17:42:47.068Z"}
[2025-07-01T17:42:47.177Z] [INFO] GET /check-student-auth - 200 - 68ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T17:42:47.177Z"}
[2025-07-01T17:42:47.262Z] [INFO] GET /student/login?redirect=%2F - 200 - 72ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T17:42:47.262Z"}
[2025-07-01T17:42:56.281Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:42:56.281Z"}
[2025-07-01T17:42:56.631Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:42:56.631Z"}
[2025-07-01T17:42:56.744Z] [INFO] GET /student/login?redirect=%2F - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:42:56.744Z"}
[2025-07-01T17:42:58.168Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-01T17:42:58.170Z] [INFO] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:42:58.170Z"}
[2025-07-01T17:43:03.863Z] [INFO] GET /student/login?redirect=%2F - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:43:03.863Z"}
[2025-07-01T17:43:03.901Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-01T17:43:03.906Z] [INFO] GET / - 404 - 7ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"7ms","timestamp":"2025-07-01T17:43:03.906Z"}
[2025-07-01T17:43:04.910Z] [ERROR] FATAL: Uncaught Exception | {"error":"listen EADDRINUSE: address already in use :::3003","stack":"Error: listen EADDRINUSE: address already in use :::3003\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\OnluyenVatLy\\api\\index.js:139:20)\n    at Module._compile (node:internal/modules/cjs/loader:1562:14)\n    at Object..js (node:internal/modules/cjs/loader:1699:10)\n    at Module.load (node:internal/modules/cjs/loader:1313:32)\n    at Function._load (node:internal/modules/cjs/loader:1123:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:217:24)\n    at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:170:5)\n    at node:internal/main/run_main_module:36:49"}
[2025-07-01T17:43:06.307Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:43:06.307Z"}
[2025-07-01T17:43:06.362Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-01T17:43:06.364Z] [INFO] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:43:06.364Z"}
[2025-07-01T17:43:06.886Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:43:06.886Z"}
[2025-07-01T17:43:07.043Z] [INFO] GET /student/login?redirect=%2F - 200 - 1ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Linux; Android 13; SM-G981B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:43:07.043Z"}
[2025-07-01T17:43:07.104Z] [WARN] 404 Not Found | {"url":"/.well-known/appspecific/com.chrome.devtools.json","method":"GET","ip":"::1"}
[2025-07-01T17:43:07.106Z] [INFO] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:43:07.106Z"}
[2025-07-01T17:43:18.087Z] [INFO] SIGINT received, shutting down gracefully
[2025-07-01T17:43:49.034Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:43:49.034Z"}
[2025-07-01T17:43:52.504Z] [INFO] GET /student/login?redirect=%2F - 200 - 16ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"16ms","timestamp":"2025-07-01T17:43:52.504Z"}
[2025-07-01T17:43:54.377Z] [INFO] GET / - 304 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:43:54.377Z"}
[2025-07-01T17:43:54.605Z] [INFO] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:43:54.605Z"}
[2025-07-01T17:43:54.741Z] [INFO] GET /student/login?redirect=%2F - 200 - 3ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:43:54.741Z"}
[2025-07-01T17:44:01.494Z] [INFO] GET /student/login - 200 - 5ms | {"method":"GET","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:44:01.494Z"}
[2025-07-01T17:44:10.297Z] [INFO] GET / - 200 - 4ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:44:10.297Z"}
[2025-07-01T17:44:10.345Z] [INFO] GET /css/style.css - 200 - 4ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:44:10.345Z"}
[2025-07-01T17:44:10.348Z] [INFO] GET /js/network-animation.js - 200 - 5ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:44:10.348Z"}
[2025-07-01T17:44:10.414Z] [INFO] GET /js/index.js - 200 - 2ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:44:10.414Z"}
[2025-07-01T17:44:10.575Z] [INFO] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:44:10.575Z"}
[2025-07-01T17:44:10.586Z] [INFO] GET /student/login?redirect=%2F - 200 - 5ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:44:10.586Z"}
[2025-07-01T17:44:10.624Z] [INFO] GET /js/device-id.js - 200 - 4ms | {"method":"GET","url":"/js/device-id.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:44:10.624Z"}
[2025-07-01T17:44:12.260Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:44:12.260Z"}
[2025-07-01T17:44:12.310Z] [INFO] GET /check-student-auth - 200 - 2ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:44:12.310Z"}
[2025-07-01T17:44:12.333Z] [INFO] GET /student/login?redirect=%2F - 200 - 1ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:44:12.333Z"}
[2025-07-01T17:44:17.704Z] [INFO] POST /student/login - 200 - 1126ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1126ms","timestamp":"2025-07-01T17:44:17.704Z"}
[2025-07-01T17:44:18.292Z] [INFO] GET / - 304 - 71ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"71ms","timestamp":"2025-07-01T17:44:18.292Z"}
[2025-07-01T17:44:18.403Z] [INFO] GET /check-student-auth - 200 - 71ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"71ms","timestamp":"2025-07-01T17:44:18.403Z"}
[2025-07-01T17:44:18.486Z] [INFO] GET /student/login?redirect=%2F - 200 - 73ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"73ms","timestamp":"2025-07-01T17:44:18.485Z"}
[2025-07-01T17:51:42.717Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:51:42.717Z"}
[2025-07-01T17:51:54.582Z] [INFO] GET /student/login?redirect=%2F - 200 - 364ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"364ms","timestamp":"2025-07-01T17:51:54.582Z"}
[2025-07-01T17:51:56.815Z] [INFO] POST /student/login - 400 - 94ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"94ms","timestamp":"2025-07-01T17:51:56.815Z"}
[2025-07-01T17:51:59.191Z] [INFO] GET /student/login?redirect=%2F - 200 - 73ms | {"method":"GET","url":"/student/login?redirect=%2F","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"73ms","timestamp":"2025-07-01T17:51:59.191Z"}
[2025-07-01T17:51:59.907Z] [INFO] GET /images/lesson2.jpg - 200 - 2ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:51:59.907Z"}
[2025-07-01T17:51:59.921Z] [INFO] GET /images/lesson4.jpg - 200 - 7ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:51:59.921Z"}
[2025-07-01T17:51:59.923Z] [INFO] GET /js/landing.js - 200 - 7ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:51:59.923Z"}
[2025-07-01T17:51:59.925Z] [INFO] GET /images/lesson3.jpg - 200 - 12ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:51:59.925Z"}
[2025-07-01T17:51:59.928Z] [INFO] GET / - 200 - 86ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"86ms","timestamp":"2025-07-01T17:51:59.928Z"}
[2025-07-01T17:51:59.930Z] [INFO] GET /images/lesson1.jpg - 200 - 30ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"30ms","timestamp":"2025-07-01T17:51:59.929Z"}
[2025-07-01T17:52:04.819Z] [INFO] GET /lessons - 200 - 68ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T17:52:04.819Z"}
[2025-07-01T17:52:04.893Z] [INFO] GET /check-student-auth - 200 - 70ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-01T17:52:04.893Z"}
[2025-07-01T17:52:04.976Z] [INFO] GET /student/login?redirect=%2Flessons - 200 - 72ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"72ms","timestamp":"2025-07-01T17:52:04.976Z"}
[2025-07-01T17:52:06.267Z] [INFO] GET / - 200 - 7ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:52:06.267Z"}
[2025-07-01T17:52:10.276Z] [INFO] GET /lessons - 200 - 4ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:52:10.276Z"}
[2025-07-01T17:52:10.890Z] [INFO] GET /check-student-auth - 200 - 3ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:52:10.890Z"}
[2025-07-01T17:52:11.104Z] [INFO] GET /student/login?redirect=%2Flessons - 200 - 3ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:52:11.104Z"}
[2025-07-01T17:52:14.368Z] [INFO] POST /student/login - 400 - 70ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"70ms","timestamp":"2025-07-01T17:52:14.368Z"}
[2025-07-01T17:52:18.776Z] [INFO] GET / - 304 - 67ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T17:52:18.776Z"}
[2025-07-01T17:52:20.088Z] [INFO] GET /lessons - 304 - 69ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T17:52:20.088Z"}
[2025-07-01T17:52:20.207Z] [INFO] GET /check-student-auth - 200 - 66ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"66ms","timestamp":"2025-07-01T17:52:20.207Z"}
[2025-07-01T17:52:20.293Z] [INFO] GET /student/login?redirect=%2Flessons - 200 - 69ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T17:52:20.293Z"}
[2025-07-01T17:52:32.729Z] [INFO] GET /js/gallery.js - 200 - 3ms | {"method":"GET","url":"/js/gallery.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:52:32.729Z"}
[2025-07-01T17:52:32.737Z] [INFO] GET /gallery - 200 - 542ms | {"method":"GET","url":"/gallery","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"542ms","timestamp":"2025-07-01T17:52:32.737Z"}
[2025-07-01T17:52:32.976Z] [WARN] 404 Not Found | {"url":"/css/gallery.css","method":"GET","ip":"::1"}
[2025-07-01T17:52:33.015Z] [INFO] GET / - 404 - 290ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"290ms","timestamp":"2025-07-01T17:52:33.015Z"}
[2025-07-01T17:52:33.054Z] [INFO] GET /check-student-auth - 200 - 67ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:52:33.054Z"}
[2025-07-01T17:52:33.089Z] [WARN] 404 Not Found | {"url":"/api/gallery-images","method":"GET","ip":"::1"}
[2025-07-01T17:52:33.124Z] [INFO] GET / - 404 - 67ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"67ms","timestamp":"2025-07-01T17:52:33.124Z"}
[2025-07-01T17:52:55.804Z] [INFO] GET / - 304 - 232ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"232ms","timestamp":"2025-07-01T17:52:55.804Z"}
[2025-07-01T17:52:56.830Z] [INFO] GET /quizgame - 200 - 76ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-01T17:52:56.830Z"}
[2025-07-01T17:52:57.034Z] [INFO] GET /js/quizgame.js - 200 - 1ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:52:57.034Z"}
[2025-07-01T17:52:57.053Z] [INFO] GET /audio/5sec_1.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-01T17:52:57.053Z"}
[2025-07-01T17:52:57.060Z] [INFO] GET /audio/30sec_1.mp3 - 206 - 20ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"20ms","timestamp":"2025-07-01T17:52:57.060Z"}
[2025-07-01T17:52:57.062Z] [INFO] GET /audio/5sec_2.mp3 - 206 - 6ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"6ms","timestamp":"2025-07-01T17:52:57.062Z"}
[2025-07-01T17:52:57.065Z] [INFO] GET /audio/5sec_3.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-01T17:52:57.065Z"}
[2025-07-01T17:52:57.067Z] [INFO] GET /audio/30sec_3.mp3 - 206 - 24ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"24ms","timestamp":"2025-07-01T17:52:57.067Z"}
[2025-07-01T17:52:57.068Z] [INFO] GET /audio/30sec_2.mp3 - 206 - 26ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"26ms","timestamp":"2025-07-01T17:52:57.068Z"}
[2025-07-01T17:52:57.078Z] [INFO] GET /audio/correct_1.mp3 - 206 - 5ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"5ms","timestamp":"2025-07-01T17:52:57.078Z"}
[2025-07-01T17:52:57.081Z] [INFO] GET /audio/correct_2.mp3 - 206 - 7ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"7ms","timestamp":"2025-07-01T17:52:57.081Z"}
[2025-07-01T17:52:57.083Z] [INFO] GET /audio/correct_3.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-01T17:52:57.083Z"}
[2025-07-01T17:52:57.085Z] [INFO] GET /audio/correct_4.mp3 - 206 - 10ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"10ms","timestamp":"2025-07-01T17:52:57.085Z"}
[2025-07-01T17:52:57.087Z] [INFO] GET /audio/correct_5.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T17:52:57.087Z"}
[2025-07-01T17:52:57.088Z] [INFO] GET /audio/incorrect.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-01T17:52:57.088Z"}
[2025-07-01T17:52:57.090Z] [INFO] GET /audio/points.mp3 - 206 - 4ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"4ms","timestamp":"2025-07-01T17:52:57.090Z"}
[2025-07-01T17:53:00.313Z] [INFO] GET /leaderboard - 200 - 98ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"98ms","timestamp":"2025-07-01T17:53:00.313Z"}
[2025-07-01T17:53:00.376Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=all","method":"GET","ip":"::1"}
[2025-07-01T17:53:00.444Z] [INFO] GET /?page=1&filter=all - 404 - 118ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"118ms","timestamp":"2025-07-01T17:53:00.444Z"}
[2025-07-01T17:53:02.776Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=month","method":"GET","ip":"::1"}
[2025-07-01T17:53:02.807Z] [INFO] GET /?page=1&filter=month - 404 - 61ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"61ms","timestamp":"2025-07-01T17:53:02.807Z"}
[2025-07-01T17:53:03.400Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=week","method":"GET","ip":"::1"}
[2025-07-01T17:53:03.432Z] [INFO] GET /?page=1&filter=week - 404 - 61ms | {"method":"GET","url":"/?page=1&filter=week","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"61ms","timestamp":"2025-07-01T17:53:03.432Z"}
[2025-07-01T17:53:04.026Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=all","method":"GET","ip":"::1"}
[2025-07-01T17:53:04.059Z] [INFO] GET /?page=1&filter=all - 404 - 78ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"78ms","timestamp":"2025-07-01T17:53:04.059Z"}
[2025-07-01T17:53:36.869Z] [INFO] GET / - 304 - 313ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"313ms","timestamp":"2025-07-01T17:53:36.869Z"}
[2025-07-01T17:53:45.652Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:53:45.652Z"}
[2025-07-01T17:54:11.952Z] [INFO] GET /lessons?v=1 - 200 - 16ms | {"method":"GET","url":"/lessons?v=1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"16ms","timestamp":"2025-07-01T17:54:11.952Z"}
[2025-07-01T17:54:12.500Z] [INFO] GET /check-student-auth - 200 - 4ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:54:12.500Z"}
[2025-07-01T17:54:12.646Z] [INFO] GET /student/login?redirect=%2Flessons%3Fv%3D1 - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons%3Fv%3D1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:12.646Z"}
[2025-07-01T17:54:18.164Z] [INFO] GET / - 304 - 323ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"323ms","timestamp":"2025-07-01T17:54:18.164Z"}
[2025-07-01T17:54:19.191Z] [INFO] GET /lessons - 304 - 62ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"62ms","timestamp":"2025-07-01T17:54:19.191Z"}
[2025-07-01T17:54:19.294Z] [INFO] GET /check-student-auth - 200 - 63ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"63ms","timestamp":"2025-07-01T17:54:19.294Z"}
[2025-07-01T17:54:19.371Z] [INFO] GET /student/login?redirect=%2Flessons - 200 - 67ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:54:19.371Z"}
[2025-07-01T17:54:21.637Z] [INFO] POST /student/login - 400 - 83ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"83ms","timestamp":"2025-07-01T17:54:21.637Z"}
[2025-07-01T17:54:23.001Z] [INFO] POST /student/login - 400 - 67ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"67ms","timestamp":"2025-07-01T17:54:23.001Z"}
[2025-07-01T17:54:28.466Z] [INFO] GET /student/login?redirect=%2Flessons - 200 - 67ms | {"method":"GET","url":"/student/login?redirect=%2Flessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T17:54:28.466Z"}
[2025-07-01T17:54:31.410Z] [INFO] POST /student/login - 400 - 62ms | {"method":"POST","url":"/student/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"62ms","timestamp":"2025-07-01T17:54:31.410Z"}
[2025-07-01T17:54:44.704Z] [INFO] GET / - 200 - 4ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T17:54:44.704Z"}
[2025-07-01T17:54:44.771Z] [INFO] GET /images/lesson1.jpg - 200 - 7ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"7ms","timestamp":"2025-07-01T17:54:44.771Z"}
[2025-07-01T17:54:44.776Z] [INFO] GET /images/lesson2.jpg - 200 - 10ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"10ms","timestamp":"2025-07-01T17:54:44.776Z"}
[2025-07-01T17:54:44.780Z] [INFO] GET /js/landing.js - 200 - 12ms | {"method":"GET","url":"/js/landing.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:54:44.780Z"}
[2025-07-01T17:54:44.781Z] [INFO] GET /js/network-animation.js - 304 - 3ms | {"method":"GET","url":"/js/network-animation.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"3ms","timestamp":"2025-07-01T17:54:44.781Z"}
[2025-07-01T17:54:44.784Z] [INFO] GET /images/lesson3.jpg - 200 - 15ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"15ms","timestamp":"2025-07-01T17:54:44.784Z"}
[2025-07-01T17:54:44.786Z] [INFO] GET /css/style.css - 200 - 12ms | {"method":"GET","url":"/css/style.css","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"12ms","timestamp":"2025-07-01T17:54:44.786Z"}
[2025-07-01T17:54:44.883Z] [INFO] GET /images/lesson4.jpg - 200 - 1ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:44.883Z"}
[2025-07-01T17:54:46.374Z] [INFO] GET /lessons - 200 - 2ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:46.374Z"}
[2025-07-01T17:54:46.410Z] [INFO] GET /js/index.js - 200 - 1ms | {"method":"GET","url":"/js/index.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:46.410Z"}
[2025-07-01T17:54:46.445Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:46.445Z"}
[2025-07-01T17:54:46.473Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:54:46.475Z] [INFO] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:54:46.475Z"}
[2025-07-01T17:54:47.069Z] [INFO] GET /?page=1&limit=10&sort=newest - 200 - 591ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"591ms","timestamp":"2025-07-01T17:54:47.069Z"}
[2025-07-01T17:54:50.308Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:50.308Z"}
[2025-07-01T17:54:51.393Z] [INFO] GET /quizgame - 200 - 2ms | {"method":"GET","url":"/quizgame","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:51.393Z"}
[2025-07-01T17:54:51.427Z] [INFO] GET /js/quizgame.js - 200 - 2ms | {"method":"GET","url":"/js/quizgame.js","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:54:51.427Z"}
[2025-07-01T17:54:51.635Z] [INFO] GET /audio/5sec_1.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T17:54:51.635Z"}
[2025-07-01T17:54:51.638Z] [INFO] GET /audio/5sec_2.mp3 - 206 - 14ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"14ms","timestamp":"2025-07-01T17:54:51.638Z"}
[2025-07-01T17:54:51.655Z] [INFO] GET /audio/5sec_3.mp3 - 206 - 29ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"29ms","timestamp":"2025-07-01T17:54:51.655Z"}
[2025-07-01T17:54:51.664Z] [INFO] GET /audio/30sec_1.mp3 - 206 - 49ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"49ms","timestamp":"2025-07-01T17:54:51.664Z"}
[2025-07-01T17:54:51.746Z] [INFO] GET /audio/30sec_2.mp3 - 206 - 128ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"128ms","timestamp":"2025-07-01T17:54:51.746Z"}
[2025-07-01T17:54:51.755Z] [INFO] GET /audio/correct_1.mp3 - 206 - 103ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"103ms","timestamp":"2025-07-01T17:54:51.755Z"}
[2025-07-01T17:54:51.757Z] [INFO] GET /audio/correct_2.mp3 - 206 - 104ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"104ms","timestamp":"2025-07-01T17:54:51.757Z"}
[2025-07-01T17:54:51.762Z] [INFO] GET /audio/30sec_3.mp3 - 206 - 141ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"141ms","timestamp":"2025-07-01T17:54:51.762Z"}
[2025-07-01T17:54:51.786Z] [INFO] GET /audio/correct_3.mp3 - 206 - 64ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"64ms","timestamp":"2025-07-01T17:54:51.786Z"}
[2025-07-01T17:54:51.829Z] [INFO] GET /audio/correct_4.mp3 - 206 - 83ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"83ms","timestamp":"2025-07-01T17:54:51.829Z"}
[2025-07-01T17:54:51.846Z] [INFO] GET /audio/incorrect.mp3 - 206 - 55ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"55ms","timestamp":"2025-07-01T17:54:51.846Z"}
[2025-07-01T17:54:51.864Z] [INFO] GET /audio/correct_5.mp3 - 206 - 74ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"74ms","timestamp":"2025-07-01T17:54:51.863Z"}
[2025-07-01T17:54:51.875Z] [INFO] GET /audio/points.mp3 - 206 - 82ms | {"method":"GET","url":"/audio/points.mp3","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":206,"responseTime":"82ms","timestamp":"2025-07-01T17:54:51.875Z"}
[2025-07-01T17:54:53.144Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:53.144Z"}
[2025-07-01T17:54:53.945Z] [INFO] GET /lessons - 304 - 4ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"4ms","timestamp":"2025-07-01T17:54:53.945Z"}
[2025-07-01T17:54:54.004Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:54.004Z"}
[2025-07-01T17:54:54.011Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:54:54.014Z] [INFO] GET / - 404 - 6ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"6ms","timestamp":"2025-07-01T17:54:54.014Z"}
[2025-07-01T17:54:55.632Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:55.631Z"}
[2025-07-01T17:54:56.467Z] [INFO] GET /lessons - 304 - 2ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:54:56.467Z"}
[2025-07-01T17:54:56.517Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:56.517Z"}
[2025-07-01T17:54:56.529Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:54:56.531Z] [INFO] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:54:56.531Z"}
[2025-07-01T17:54:58.523Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:58.523Z"}
[2025-07-01T17:54:59.173Z] [INFO] GET /lessons - 304 - 1ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:54:59.173Z"}
[2025-07-01T17:54:59.225Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:54:59.225Z"}
[2025-07-01T17:54:59.235Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:54:59.238Z] [INFO] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T17:54:59.238Z"}
[2025-07-01T17:55:00.366Z] [INFO] GET / - 304 - 1ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"1ms","timestamp":"2025-07-01T17:55:00.366Z"}
[2025-07-01T17:55:02.500Z] [INFO] GET /lessons - 304 - 2ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"2ms","timestamp":"2025-07-01T17:55:02.500Z"}
[2025-07-01T17:55:02.546Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:55:02.546Z"}
[2025-07-01T17:55:02.551Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:55:02.555Z] [INFO] GET / - 404 - 4ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"4ms","timestamp":"2025-07-01T17:55:02.555Z"}
[2025-07-01T17:55:04.956Z] [INFO] GET /admin/login - 200 - 2ms | {"method":"GET","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:55:04.956Z"}
[2025-07-01T17:55:05.966Z] [INFO] GET /lessons?debug=1 - 200 - 5ms | {"method":"GET","url":"/lessons?debug=1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:55:05.966Z"}
[2025-07-01T17:55:06.707Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:55:06.707Z"}
[2025-07-01T17:55:06.896Z] [INFO] GET /student/login?redirect=%2Flessons%3Fdebug%3D1 - 200 - 2ms | {"method":"GET","url":"/student/login?redirect=%2Flessons%3Fdebug%3D1","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:55:06.896Z"}
[2025-07-01T17:55:17.482Z] [INFO] POST /admin/login - 200 - 280ms | {"method":"POST","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"280ms","timestamp":"2025-07-01T17:55:17.482Z"}
[2025-07-01T17:55:18.062Z] [INFO] GET /admin - 500 - 64ms | {"method":"GET","url":"/admin","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":500,"responseTime":"64ms","timestamp":"2025-07-01T17:55:18.062Z"}
[2025-07-01T17:55:18.116Z] [WARN] 404 Not Found | {"url":"/favicon.ico","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:55:18.117Z] [INFO] GET / - 404 - 2ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"2ms","timestamp":"2025-07-01T17:55:18.117Z"}
[2025-07-01T17:56:26.153Z] [INFO] GET /admin/login - 200 - 266ms | {"method":"GET","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"266ms","timestamp":"2025-07-01T17:56:26.153Z"}
[2025-07-01T17:56:30.631Z] [WARN] 404 Not Found | {"url":"/gogo","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:56:30.666Z] [INFO] GET / - 404 - 68ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"68ms","timestamp":"2025-07-01T17:56:30.666Z"}
[2025-07-01T17:57:34.542Z] [INFO] GET / - 304 - 308ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"308ms","timestamp":"2025-07-01T17:57:34.542Z"}
[2025-07-01T17:57:49.988Z] [INFO] GET /lessons - 304 - 295ms | {"method":"GET","url":"/lessons","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":304,"responseTime":"295ms","timestamp":"2025-07-01T17:57:49.988Z"}
[2025-07-01T17:57:50.122Z] [INFO] GET /check-student-auth - 200 - 76ms | {"method":"GET","url":"/check-student-auth","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"76ms","timestamp":"2025-07-01T17:57:50.122Z"}
[2025-07-01T17:57:50.163Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:57:50.201Z] [INFO] GET / - 404 - 76ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"76ms","timestamp":"2025-07-01T17:57:50.201Z"}
[2025-07-01T17:57:51.541Z] [INFO] GET /admin/login - 200 - 74ms | {"method":"GET","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"74ms","timestamp":"2025-07-01T17:57:51.541Z"}
[2025-07-01T17:57:58.263Z] [INFO] POST /admin/login - 400 - 73ms | {"method":"POST","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":400,"responseTime":"73ms","timestamp":"2025-07-01T17:57:58.263Z"}
[2025-07-01T17:58:05.754Z] [WARN] 404 Not Found | {"url":"/admin/edit","method":"GET","ip":"::ffff:127.0.0.1"}
[2025-07-01T17:58:05.792Z] [INFO] GET / - 404 - 75ms | {"method":"GET","url":"/","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":404,"responseTime":"75ms","timestamp":"2025-07-01T17:58:05.792Z"}
[2025-07-01T17:58:07.943Z] [INFO] GET /admin/login - 200 - 74ms | {"method":"GET","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0","statusCode":200,"responseTime":"74ms","timestamp":"2025-07-01T17:58:07.943Z"}
[2025-07-01T17:58:19.648Z] [INFO] GET / - 200 - 2ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:58:19.648Z"}
[2025-07-01T17:58:19.691Z] [INFO] GET /css/style.css - 200 - 3ms | {"method":"GET","url":"/css/style.css","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:58:19.691Z"}
[2025-07-01T17:58:19.693Z] [INFO] GET /images/lesson1.jpg - 200 - 5ms | {"method":"GET","url":"/images/lesson1.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"5ms","timestamp":"2025-07-01T17:58:19.693Z"}
[2025-07-01T17:58:19.694Z] [INFO] GET /images/lesson2.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson2.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T17:58:19.694Z"}
[2025-07-01T17:58:19.749Z] [INFO] GET /images/lesson4.jpg - 200 - 3ms | {"method":"GET","url":"/images/lesson4.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"3ms","timestamp":"2025-07-01T17:58:19.749Z"}
[2025-07-01T17:58:19.751Z] [INFO] GET /images/lesson3.jpg - 200 - 6ms | {"method":"GET","url":"/images/lesson3.jpg","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T17:58:19.751Z"}
[2025-07-01T17:58:19.753Z] [INFO] GET /js/network-animation.js - 200 - 6ms | {"method":"GET","url":"/js/network-animation.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T17:58:19.753Z"}
[2025-07-01T17:58:19.754Z] [INFO] GET /js/landing.js - 200 - 6ms | {"method":"GET","url":"/js/landing.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"6ms","timestamp":"2025-07-01T17:58:19.754Z"}
[2025-07-01T17:58:20.970Z] [INFO] GET /lessons - 200 - 2ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:58:20.970Z"}
[2025-07-01T17:58:20.997Z] [INFO] GET /js/index.js - 200 - 1ms | {"method":"GET","url":"/js/index.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:58:20.997Z"}
[2025-07-01T17:58:21.038Z] [INFO] GET /check-student-auth - 200 - 1ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"1ms","timestamp":"2025-07-01T17:58:21.038Z"}
[2025-07-01T17:58:21.043Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::1"}
[2025-07-01T17:58:21.048Z] [INFO] GET / - 404 - 6ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"6ms","timestamp":"2025-07-01T17:58:21.048Z"}
[2025-07-01T17:58:21.539Z] [INFO] GET /?page=1&limit=10&sort=newest - 200 - 486ms | {"method":"GET","url":"/?page=1&limit=10&sort=newest","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"486ms","timestamp":"2025-07-01T17:58:21.539Z"}
[2025-07-01T17:58:22.944Z] [INFO] GET /admin/login - 200 - 2ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:58:22.944Z"}
[2025-07-01T17:58:25.224Z] [INFO] POST /admin/login - 200 - 267ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"267ms","timestamp":"2025-07-01T17:58:25.224Z"}
[2025-07-01T17:58:25.794Z] [INFO] GET /admin - 500 - 56ms | {"method":"GET","url":"/admin","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":500,"responseTime":"56ms","timestamp":"2025-07-01T17:58:25.794Z"}
[2025-07-01T17:59:26.605Z] [INFO] Server running on port 3003 | {"port":3003,"environment":"development","timestamp":"2025-07-01T17:59:26.605Z"}
[2025-07-01T17:59:27.372Z] [INFO] GET /check-student-auth - 200 - 368ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"368ms","timestamp":"2025-07-01T17:59:27.372Z"}
[2025-07-01T17:59:27.409Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::1"}
[2025-07-01T17:59:27.451Z] [INFO] GET / - 404 - 77ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"77ms","timestamp":"2025-07-01T17:59:27.451Z"}
[2025-07-01T17:59:29.082Z] [INFO] GET /js/lesson.js - 200 - 2ms | {"method":"GET","url":"/js/lesson.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"2ms","timestamp":"2025-07-01T17:59:29.082Z"}
[2025-07-01T17:59:29.085Z] [INFO] GET /lesson/1748074653639 - 200 - 68ms | {"method":"GET","url":"/lesson/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T17:59:29.085Z"}
[2025-07-01T17:59:29.335Z] [WARN] 404 Not Found | {"url":"/css/lesson-questions.css","method":"GET","ip":"::1"}
[2025-07-01T17:59:29.379Z] [INFO] GET / - 404 - 301ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"301ms","timestamp":"2025-07-01T17:59:29.379Z"}
[2025-07-01T17:59:29.433Z] [INFO] GET /check-student-auth - 200 - 69ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"69ms","timestamp":"2025-07-01T17:59:29.433Z"}
[2025-07-01T17:59:29.830Z] [INFO] GET /1748074653639 - 200 - 394ms | {"method":"GET","url":"/1748074653639","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"394ms","timestamp":"2025-07-01T17:59:29.830Z"}
[2025-07-01T17:59:35.296Z] [INFO] GET / - 304 - 70ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"70ms","timestamp":"2025-07-01T17:59:35.296Z"}
[2025-07-01T17:59:36.327Z] [INFO] GET /quizgame - 200 - 70ms | {"method":"GET","url":"/quizgame","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-01T17:59:36.327Z"}
[2025-07-01T17:59:36.328Z] [INFO] GET /js/quizgame.js - 200 - 14ms | {"method":"GET","url":"/js/quizgame.js","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"14ms","timestamp":"2025-07-01T17:59:36.328Z"}
[2025-07-01T17:59:36.461Z] [INFO] GET /audio/5sec_2.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/5sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-01T17:59:36.461Z"}
[2025-07-01T17:59:36.463Z] [INFO] GET /audio/5sec_1.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/5sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T17:59:36.463Z"}
[2025-07-01T17:59:36.467Z] [INFO] GET /audio/5sec_3.mp3 - 206 - 11ms | {"method":"GET","url":"/audio/5sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"11ms","timestamp":"2025-07-01T17:59:36.467Z"}
[2025-07-01T17:59:36.475Z] [INFO] GET /audio/30sec_1.mp3 - 206 - 30ms | {"method":"GET","url":"/audio/30sec_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"30ms","timestamp":"2025-07-01T17:59:36.475Z"}
[2025-07-01T17:59:36.478Z] [INFO] GET /audio/correct_1.mp3 - 206 - 12ms | {"method":"GET","url":"/audio/correct_1.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"12ms","timestamp":"2025-07-01T17:59:36.478Z"}
[2025-07-01T17:59:36.480Z] [INFO] GET /audio/30sec_3.mp3 - 206 - 31ms | {"method":"GET","url":"/audio/30sec_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"31ms","timestamp":"2025-07-01T17:59:36.480Z"}
[2025-07-01T17:59:36.483Z] [INFO] GET /audio/correct_2.mp3 - 206 - 13ms | {"method":"GET","url":"/audio/correct_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"13ms","timestamp":"2025-07-01T17:59:36.483Z"}
[2025-07-01T17:59:36.486Z] [INFO] GET /audio/30sec_2.mp3 - 206 - 38ms | {"method":"GET","url":"/audio/30sec_2.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"38ms","timestamp":"2025-07-01T17:59:36.486Z"}
[2025-07-01T17:59:36.491Z] [INFO] GET /audio/correct_3.mp3 - 206 - 18ms | {"method":"GET","url":"/audio/correct_3.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"18ms","timestamp":"2025-07-01T17:59:36.491Z"}
[2025-07-01T17:59:36.494Z] [INFO] GET /audio/correct_5.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/correct_5.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-01T17:59:36.494Z"}
[2025-07-01T17:59:36.495Z] [INFO] GET /audio/correct_4.mp3 - 206 - 10ms | {"method":"GET","url":"/audio/correct_4.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"10ms","timestamp":"2025-07-01T17:59:36.495Z"}
[2025-07-01T17:59:36.497Z] [INFO] GET /audio/incorrect.mp3 - 206 - 8ms | {"method":"GET","url":"/audio/incorrect.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"8ms","timestamp":"2025-07-01T17:59:36.497Z"}
[2025-07-01T17:59:36.499Z] [INFO] GET /audio/points.mp3 - 206 - 9ms | {"method":"GET","url":"/audio/points.mp3","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":206,"responseTime":"9ms","timestamp":"2025-07-01T17:59:36.499Z"}
[2025-07-01T17:59:38.774Z] [INFO] GET / - 304 - 69ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"69ms","timestamp":"2025-07-01T17:59:38.774Z"}
[2025-07-01T17:59:39.471Z] [INFO] GET /leaderboard - 200 - 70ms | {"method":"GET","url":"/leaderboard","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"70ms","timestamp":"2025-07-01T17:59:39.471Z"}
[2025-07-01T17:59:39.732Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=all","method":"GET","ip":"::1"}
[2025-07-01T17:59:39.772Z] [INFO] GET /?page=1&filter=all - 404 - 303ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"303ms","timestamp":"2025-07-01T17:59:39.772Z"}
[2025-07-01T17:59:41.230Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=month","method":"GET","ip":"::1"}
[2025-07-01T17:59:41.270Z] [INFO] GET /?page=1&filter=month - 404 - 79ms | {"method":"GET","url":"/?page=1&filter=month","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"79ms","timestamp":"2025-07-01T17:59:41.270Z"}
[2025-07-01T17:59:41.796Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=week","method":"GET","ip":"::1"}
[2025-07-01T17:59:41.836Z] [INFO] GET /?page=1&filter=week - 404 - 79ms | {"method":"GET","url":"/?page=1&filter=week","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"79ms","timestamp":"2025-07-01T17:59:41.836Z"}
[2025-07-01T17:59:42.111Z] [WARN] 404 Not Found | {"url":"/api/ratings?page=1&filter=all","method":"GET","ip":"::1"}
[2025-07-01T17:59:42.151Z] [INFO] GET /?page=1&filter=all - 404 - 79ms | {"method":"GET","url":"/?page=1&filter=all","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"79ms","timestamp":"2025-07-01T17:59:42.151Z"}
[2025-07-01T18:00:00.559Z] [INFO] GET / - 304 - 555ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"555ms","timestamp":"2025-07-01T18:00:00.559Z"}
[2025-07-01T18:00:07.299Z] [INFO] GET /lessons - 304 - 64ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"64ms","timestamp":"2025-07-01T18:00:07.299Z"}
[2025-07-01T18:00:07.408Z] [INFO] GET /check-student-auth - 200 - 64ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"64ms","timestamp":"2025-07-01T18:00:07.408Z"}
[2025-07-01T18:00:07.441Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::1"}
[2025-07-01T18:00:07.475Z] [INFO] GET / - 404 - 65ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"65ms","timestamp":"2025-07-01T18:00:07.475Z"}
[2025-07-01T18:00:08.954Z] [INFO] GET /admin/login - 200 - 67ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"67ms","timestamp":"2025-07-01T18:00:08.954Z"}
[2025-07-01T18:00:11.121Z] [INFO] POST /admin/login - 400 - 76ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"76ms","timestamp":"2025-07-01T18:00:11.121Z"}
[2025-07-01T18:00:13.586Z] [INFO] GET /admin/login - 200 - 62ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"62ms","timestamp":"2025-07-01T18:00:13.586Z"}
[2025-07-01T18:00:17.429Z] [INFO] POST /admin/login - 400 - 68ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"68ms","timestamp":"2025-07-01T18:00:17.429Z"}
[2025-07-01T18:00:18.427Z] [INFO] POST /admin/login - 400 - 68ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"68ms","timestamp":"2025-07-01T18:00:18.427Z"}
[2025-07-01T18:00:19.115Z] [INFO] GET / - 304 - 68ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"68ms","timestamp":"2025-07-01T18:00:19.115Z"}
[2025-07-01T18:00:20.637Z] [INFO] GET /lessons - 304 - 67ms | {"method":"GET","url":"/lessons","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":304,"responseTime":"67ms","timestamp":"2025-07-01T18:00:20.637Z"}
[2025-07-01T18:00:20.744Z] [INFO] GET /check-student-auth - 200 - 64ms | {"method":"GET","url":"/check-student-auth","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"64ms","timestamp":"2025-07-01T18:00:20.744Z"}
[2025-07-01T18:00:20.778Z] [WARN] 404 Not Found | {"url":"/api/tags","method":"GET","ip":"::1"}
[2025-07-01T18:00:20.812Z] [INFO] GET / - 404 - 66ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":404,"responseTime":"66ms","timestamp":"2025-07-01T18:00:20.812Z"}
[2025-07-01T18:00:21.935Z] [INFO] GET /admin/login - 200 - 68ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"68ms","timestamp":"2025-07-01T18:00:21.935Z"}
[2025-07-01T18:00:23.789Z] [INFO] POST /admin/login - 400 - 314ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":400,"responseTime":"314ms","timestamp":"2025-07-01T18:00:23.789Z"}
[2025-07-01T18:00:40.256Z] [INFO] GET /admin/login - 200 - 281ms | {"method":"GET","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0","statusCode":200,"responseTime":"281ms","timestamp":"2025-07-01T18:00:40.256Z"}
[2025-07-01T18:00:47.122Z] [INFO] POST /admin/login - 400 - 67ms | {"method":"POST","url":"/admin/login","ip":"::ffff:127.0.0.1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0","statusCode":400,"responseTime":"67ms","timestamp":"2025-07-01T18:00:47.122Z"}
[2025-07-01T18:01:01.424Z] [INFO] GET /admin/login - 200 - 4ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","statusCode":200,"responseTime":"4ms","timestamp":"2025-07-01T18:01:01.424Z"}
[2025-07-01T18:01:04.386Z] [WARN] 404 Not Found | {"url":"/favicon.ico","method":"GET","ip":"::1"}
[2025-07-01T18:01:04.388Z] [INFO] GET / - 404 - 3ms | {"method":"GET","url":"/","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","statusCode":404,"responseTime":"3ms","timestamp":"2025-07-01T18:01:04.388Z"}
[2025-07-01T18:01:06.227Z] [INFO] POST /api/auth/admin/login - 500 - 6ms | {"method":"POST","url":"/api/auth/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","statusCode":500,"responseTime":"6ms","timestamp":"2025-07-01T18:01:06.227Z"}
[2025-07-01T18:01:35.794Z] [INFO] GET /admin/login - 200 - 25ms | {"method":"GET","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":200,"responseTime":"25ms","timestamp":"2025-07-01T18:01:35.794Z"}
[2025-07-01T18:01:38.167Z] [INFO] POST /admin/login - 429 - 0ms | {"method":"POST","url":"/admin/login","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","statusCode":429,"responseTime":"0ms","timestamp":"2025-07-01T18:01:38.166Z"}
[2025-07-01T18:02:01.858Z] [INFO] SIGINT received, shutting down gracefully
[2025-07-01T18:02:01.860Z] [INFO] Process terminated
